<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <wd-navbar :bordered="false" custom-class="!bg-transparent" placeholder safe-area-inset-top>
        <!-- <template #left>
          <view class="flex items-center gap-30rpx" @click="goInfo">
            <wd-icon :name="messagePromptImg" size="54rpx" />
          </view>
        </template> -->
        <template #right>
          <view class="flex items-center" @click="goSeeting">
            <wd-img :src="setupImg" height="58rpx" width="58rpx" />
          </view>
        </template>
      </wd-navbar>
    </template>
    <view class="px-50rpx">
      <view
        class="flex items-center py-32rpx border-b-1px border-b-solid border-b-[#BDBDBD]"
        @click="goPersonalInfo"
      >
        <view class="flex items-center gap-24rpx flex-1">
          <wd-img :src="myObj.headImgUrl" height="110rpx" round width="110rpx" />

          <view class="flex flex-col gap-14rpx">
            <view class="flex items-center line-clamp-1">
              <text class="c-#333333 text-32rpx font-500">{{ myObj.trueName }}</text>
              <text v-if="myObj?.hrPosition">&nbsp;·&nbsp;</text>
              <text v-if="myObj?.hrPosition" class="c-#555555 text-28rpx">
                {{ myObj.hrPosition ? myObj.hrPosition : '' }}
              </text>
            </view>
            <text class="c-#333333 text-28rpx line-clamp-1">
              {{ truncateText(myObj.companyName, 14) }}
            </text>
          </view>
        </view>
        <wd-icon color="#555555" name="arrow-right" size="40rpx" />
      </view>
      <view class="flex flex-col gap-60rpx mt-32rpx">
        <view class="grid grid-cols-4 mx--50rpx">
          <view
            v-for="(item, key) in newsList"
            :key="`news-${key}`"
            class="flex flex-col items-center gap-10px"
            @click="goNewsPage(key)"
          >
            <text class="c-#555555 text-24rpx">{{ item.name }}</text>
            <text class="c-#333333 text-36rpx font-bold">{{ item.num }}</text>
          </view>
        </view>

        <!-- 岗位在招 -->
        <view class="recruit-card" @click="goRecruitPositions">
          <view class="recruit-card-content">
            <!-- 左侧内容 -->
            <view class="items-center">
              <view class="recruit-title-row">
                <view class="recruit-title">岗位管理</view>
                <image
                  class="recruit-arrow-line"
                  mode="widthFix"
                  src="/static/my/business/line.png"
                />
              </view>
              <view class="recruit-list">
                <text class="recruit-positions-text">
                  {{ recruitPositions.join(' / ') }}
                </text>
              </view>
            </view>
            <!-- 右侧指标 -->
            <view class="items-center-right">
              <image
                class="recruit-arrow"
                mode="aspectFit"
                src="/static/my/business/arrow_block.png"
              />
            </view>
          </view>
        </view>

        <view class="grid grid-cols-4 mx--10rpx gap-30rpx mt--20rpx">
          <view
            v-for="(item, key) in selectList"
            :key="`select-${key}`"
            class="flex flex-col items-center gap-10px"
            @click="changeHandel(key)"
          >
            <view class="relative w-68rpx h-68rpx flex p-6rpx">
              <wd-img :src="`/static/my/business/${item.icon}.png`" height="100%" width="100%" />
            </view>
            <text class="c-#000000 text-26rpx">{{ item.title }}</text>
          </view>
        </view>
        <!-- <view
          class="h-196rpx bg-cover bg-no-repeat bg-center border-rd-40rpx flex flex-col justify-center box-border pl-32rpx pr-134rpx gap-16rpx"
          :style="{ backgroundImage: `url(${bombCardBg})` }"
        >
          <wd-img :src="bombCardTitle" width="154rpx" height="46rpx" />
          <text class="text-22rpx c-#000000 line-clamp-2">
            使用规则使用规则使用规则使用规则使用
          </text>
        </view> -->
      </view>
    </view>
    <view class="end_image_box mt-45rpx">
      <image class="end_image" mode="widthFix" src="/static/my/companyMy.png" />
    </view>
    <template #bottom>
      <view class="m-b-20rpx">
        <CommonLink></CommonLink>
      </view>
      <customTabbar name="mine" />
    </template>
  </z-paging>
  <!-- <orbital-menu :items="selectList" /> -->
</template>

<script lang="ts" setup>
import { truncateText } from '@/utils/util'
import CommonLink from '@/components/CommonLink/CommonLinkMy.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import orbitalMenu from '@/components/common/orbital-menu.vue'
import { myTotalList, hrUserInfo } from '@/service/hrUser'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
import setupImg from '@/static/common/setup.png'
import messagePromptImg from '@/static/common/message-prompt.png'
import bombCardTitle from '@/static/mine/business/bomb-card-title.png'
import bombCardBg from '@/static/mine/business/bomb-card-bg.png'
import { useLoginStore } from '@/store'
import ItemContainer from '@/ChatUIKit/modules/Chat/components/MessageInputToolBar/itemContainer.vue'
import { queryMyPositionListByStatus } from '@/interPost/my'

defineOptions({
  name: 'MineBusiness',
})
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// 我的信息
const myObj = ref<AnyObject>({})
// vuex数据
const loginStore = useLoginStore()
// 招聘岗位列表
const recruitPositions = ref([])

const positionParams = reactive({
  entity: {
    isRecruit: 0,
    status: 1,
  },
  orderBy: {},
  page: 1,
  size: 10,
})
const newsList = reactive([
  {
    name: '面试',
    num: 0,
  },
  {
    name: '沟通过',
    num: 0,
  },
  {
    name: '被收藏',
    num: 0,
  },
  {
    name: '不合适',
    num: 0,
  },
])
const selectList = reactive([
  {
    icon: 'mine_icon_1',
    title: '招聘数据',
    url: '/sub_business/pages/recruitmentData/index',
  },
  {
    icon: 'mine_icon_2',
    title: '公司信息',
    url: '/sub_business/pages/company/index',
  },
  {
    icon: 'mine_icon_3',
    title: '地址管理',
    url: '/sub_business/pages/AddressCenter/index',
  },
  {
    icon: 'mine_icon_4',
    title: '我的道具',
    url: '/sub_business/pages/prop/index',
  },
  {
    icon: 'mine_icon_5',
    title: '消息中心',
    url: '/chartPage/message/index',
  },
  {
    icon: 'mine_icon_6',
    title: '钱包发票',
    url: '/sub_business/pages/walletInvoice/index',
  },
  {
    icon: 'mine_icon_7',
    title: '我的客服',
    url: '/sub_business/pages/service/index',
  },
  {
    icon: 'mine_icon_8',
    title: '隐私协议',
    url: '/sub_business/pages/setting/model/PrivacyAgreement',
  },
])
// hr基本信息列表
const myList = async () => {
  const res: any = await hrUserInfo()
  console.log(res, '===========')
  if (res.code === 0) {
    myObj.value = res.data
    console.log(myObj.value, '================================')
    if (res.data.headImgUrl) {
      myObj.value.headImgUrl = res.data.headImgUrl
    } else {
      myObj.value.headImgUrl =
        myObj.value.gender === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
    }
  }
}

// 获取在招岗位
const getRecruitPositions = async () => {
  const res: any = await queryMyPositionListByStatus({
    ...positionParams,
  })
  if (res.code === 0) {
    const positions = res.data?.list?.map((item) => item.positionName) || []
    recruitPositions.value = positions.length ? positions : ['暂无岗位在招']
  }
}
const goNewsPage = (index: number) => {
  if (index === 0) {
    uni.navigateTo({
      url: '/sub_business/pages/interview/index',
    })
  }
  if (index === 1) {
    uni.navigateTo({
      url: '/sub_business/pages/communicate/index',
    })
  }
  if (index === 2) {
    uni.navigateTo({
      url: '/sub_business/pages/tucked/index',
    })
  }
  if (index === 3) {
    uni.navigateTo({
      url: '/sub_business/pages/inappropriate/index',
    })
  }
}

// 消息
const goInfo = () => {
  uni.navigateTo({
    url: '/chartPage/message/index',
  })
}

const goSeeting = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/index',
  })
}
// 去我的信息
const goPersonalInfo = () => {
  uni.navigateTo({
    url: '/sub_business/pages/myInfo/index',
  })
}
// 获取简历沟通
const myTotalListFun = async () => {
  const res: any = await myTotalList()
  if (res.code === 0) {
    newsList[0].num = res.data.interviewCount
    newsList[1].num = res.data.interactCount
    newsList[2].num = res.data.beenCollectCount
    newsList[3].num = res.data.unInterestCount
  }
}

// 支付
const changeHandel = (index: number) => {
  const item = selectList[index]
  if (item.url) {
    uni.navigateTo({
      url: item.url,
    })
  }
}

// 岗位在招
const goRecruitPositions = () => {
  uni.navigateTo({
    url: '/sub_business/pages/positionManage/index',
  })
}
const mineShow = () => {
  myList()
  myTotalListFun()
  getRecruitPositions()
}
defineExpose({
  mineShow,
})
</script>

<style lang="scss" scoped>
.end_image_box {
  box-sizing: border-box;
  width: 100%;
  padding: 40rpx;

  image {
    width: 100%;
    height: 400rpx;
  }
}

.recruit-card {
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 100%;
  height: 156rpx;
  padding: 0 32rpx;
  margin-bottom: 20rpx;
  background: url('@/static/my/business/positions_recruiting.png') no-repeat center/cover;
  border-radius: 24rpx;

  .recruit-card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .items-center {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .recruit-title-row {
        display: flex;
        align-items: center;
      }

      .recruit-title {
        margin-right: 16rpx;
        font-size: 32rpx;
        font-weight: normal;
        color: #333333;
        white-space: nowrap;
      }

      .recruit-arrow-line {
        width: 280rpx;
        height: 280rpx;
        margin-right: 32rpx;
        margin-left: 32rpx;
        vertical-align: middle;
      }

      .recruit-list {
        max-width: 400rpx;
        margin-top: 22rpx;
        font-size: 26rpx;
        color: #333333;
        text-align: left;
        opacity: 0.9;

        .recruit-positions-text {
          display: inline-block;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .items-center-right {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: -64rpx;
      margin-left: -16rpx;

      .recruit-arrow {
        width: 128rpx;
        height: 128rpx;
        vertical-align: middle;
      }
    }
  }
}
</style>
