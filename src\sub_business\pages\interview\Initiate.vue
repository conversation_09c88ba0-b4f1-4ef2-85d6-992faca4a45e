<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle" safe-area-inset-bottom>
    <template #top>
      <CustomNavBar title="发起面试" />
    </template>
    <view
      class="m-40rpx"
      :style="{ marginBottom: keyboardHeight > 0 ? `${keyboardHeight}px` : '40rpx' }"
    >
      <view class="shadow-[0px_4px_10px_0px_rgba(0,0,0,0.3)] rounded-[16rpx]">
        <ren-calendar
          v-if="fromDate.agreeTime"
          ref="ren"
          :defaultDate="fromDate.agreeTime"
          :headerBar="true"
          @onDayClick="onDayClick"
          :open="true"
          class="rounded-[16rpx]"
        ></ren-calendar>
      </view>
      <view class="shadow-[0px_4px_10px_0px_rgba(0,0,0,0.3)] rounded-[16rpx] my-40rpx bg-#fff">
        <view
          class="flex items-center justify-between py-30rpx px-30rpx"
          @click="showTime = !showTime"
        >
          <view class="flex items-center">
            <view class="text-28rpx c-#333">面试时间：</view>
            <view class="text-28rpx c-#FF7648">{{ currentDateTimes }}</view>
          </view>
          <wd-icon
            :name="showTime ? 'chevron-up' : 'chevron-down'"
            size="18px"
            color="#666"
          ></wd-icon>
        </view>
        <wd-datetime-picker-view
          type="time"
          v-model="currentDateTimes"
          label="时分"
          :columns-height="140"
          v-if="showTime"
          class="m-t-[-40rpx]"
        />
      </view>
      <view
        class="shadow-[0px_4px_10px_0px_rgba(0,0,0,0.3)] rounded-[16rpx] py-30rpx bg-#fff px-30rpx m-t-40rpx"
      >
        <view class="flex items-center justify-between">
          <view class="text-28rpx c-#333">面试方式：</view>
          <wd-radio-group
            v-model="fromDate.type"
            inline
            shape="dot"
            checked-color="#FF7648"
            :disabled="!!paramsId"
          >
            <wd-radio :value="0">线上</wd-radio>
            <wd-radio :value="1">线下</wd-radio>
          </wd-radio-group>
        </view>
      </view>
      <view
        class="shadow-[0px_4px_10px_0px_rgba(0,0,0,0.3)] rounded-[16rpx] py-40rpx bg-#fff px-30rpx m-t-40rpx"
      >
        <view
          v-if="fromDate.type === 1"
          class="p-b-10rpx border-b border-b-#E5E5E5 border-b-solid border-b-1 m-b-20rpx"
          @click="handleAddress"
        >
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">面试地点：</view>
            <wd-icon name="chevron-right" size="18px" color="#666" v-if="!paramsId" />
          </view>
          <view
            class="text-28rpx line-height-68rpx"
            :class="initiateAddress ? 'c-#333' : 'c-#bcbcbc'"
          >
            {{ initiateAddress || '请选择' }}
          </view>
        </view>
        <view class="p-b-10rpx m-b-20rpx border-b border-b-#E5E5E5 border-b-solid border-b-1">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">联系人：</view>
          </view>
          <wd-input
            no-border
            type="text"
            v-model="fromDate.hrName"
            :adjust-position="false"
            @keyboardheightchange="handleKeyboardHeightChange"
            placeholder="请输入联系人"
            :readonly="!!paramsId"
          />
        </view>
        <view class="p-b-10rpx border-b border-b-#E5E5E5 border-b-solid border-b-1">
          <view class="flex items-center justify-between">
            <view class="text-28rpx c-#333">联系电话：</view>
          </view>
          <wd-input
            type="text"
            no-border
            v-model="fromDate.hrPhone"
            placeholder="请输入手机号"
            :adjust-position="false"
            @keyboardheightchange="handleKeyboardHeightChange"
            :readonly="!!paramsId"
          />
        </view>
      </view>

      <view
        class="shadow-[0px_4px_10px_0px_rgba(0,0,0,0.3)] rounded-[16rpx] py-10rpx bg-#fff m-t-40rpx text-28rpx c-#333"
      >
        <wd-textarea
          custom-class="custom-class"
          v-model="fromDate.remark"
          :adjust-position="false"
          @keyboardheightchange="handleKeyboardHeightChange"
          custom-textarea-container-class="custom-textarea-container-class"
          placeholder="填写需要携带的资料，作品，简历等..."
        />
      </view>
    </view>
    <template #bottom>
      <view class="btn_fixed" @click="handleSubmit">
        <view class="btn_box btn_box_publish">
          <view class="btn_bg btn_bg_publish">发起面试</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { formatDateDay } from '@/utils/common'
import { regPhone } from '@/utils/rule'
import { valueOfToDate } from '@/utils'
import { EMIT_EVENT } from '@/enum'
import { interviewAdd, queryInterviewRecordById, hrInterviewUpdate } from '@/interPost/resume'
import { hrInterviewRecordUpdateById } from '@/service/hrInterviewRecord'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import RenCalendar from '@/components/ren-calendar/ren-calendar.vue'

const { userIntel } = useUserInfo()
const { pageParams } = usePagePeriod()
const { releaseActiveAddress } = useReleasePost()
const { sendInvitationForInterview } = useIMConversation()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const initiateExtInfo = ref<Partial<Api.IM.CustomMessage.ExtInfo>>({})
const ren = ref(null)
const showTime = ref(false)
const currentDateTimes = ref('10:00')
const keyboardHeight = ref(0)
const fromDate = ref({
  agreeTime: '',
  receiveUserId: '',
  remark: '',
  value: '',
  time: '',
  hrName: '',
  hrPhone: '',
  type: 1,
})
const paramsId = computed(() => pageParams.value?.id || null)
const initiateAddress = computed(() =>
  [
    releaseActiveAddress.value?.provideName,
    releaseActiveAddress.value?.cityName,
    releaseActiveAddress.value?.districtName,
    releaseActiveAddress.value?.address,
  ]
    .filter(Boolean)
    .join(''),
)

const onDayClick = (e) => {
  fromDate.value.agreeTime = e.date
}
// 面试地址
const handleAddress = () => {
  if (!paramsId.value) {
    uni.navigateTo({
      url: '/sub_business/pages/AddressCenter/index?source=release',
    })
  }
}
// 键盘高度
const handleKeyboardHeightChange = ({ height }) => {
  keyboardHeight.value = height
}

// 提交
const handleSubmit = async () => {
  if (!currentDateTimes.value) {
    uni.showToast({
      title: '请选择面试时间',
      icon: 'none',
    })
    return
  }
  if (!initiateAddress.value) {
    uni.showToast({
      title: '请选择面试地址',
      icon: 'none',
    })
    return
  }
  if (!fromDate.value.hrName) {
    uni.showToast({
      title: '请输入联系人',
      icon: 'none',
    })
    return
  }
  if (!fromDate.value.hrPhone || !regPhone.test(fromDate.value.hrPhone)) {
    uni.showToast({
      title: '请输入正确的联系电话',
      icon: 'none',
    })
    return
  }

  const agreeTime = valueOfToDate(
    `${fromDate.value.agreeTime} ${currentDateTimes.value}`,
    'YYYY-MM-DD HH:mm:ss',
  )
  const formData = {
    ...fromDate.value,
    address: initiateAddress.value,
    addressId: releaseActiveAddress.value.id,
    agreeTime,
  }
  const submitSuccess = async (type: 'edit' | 'add', interviewId?: number) => {
    const isAdd = type === 'add'
    const id = interviewId ?? paramsId.value
    uni.$emit('refreshInterview')
    uni.$emit('refreshInterviewList')
    if (isAdd && id) {
      initiateExtInfo.value = await sendInvitationForInterview(pageParams.value.to, {
        id,
        agreeTime,
      })
    } else {
      uni.navigateBack()
    }
  }
  if (paramsId.value) {
    await hrInterviewUpdate({
      ...formData,
    })
    submitSuccess('edit')
  } else {
    const { data } = await interviewAdd({
      ...formData,
      positionId: pageParams.value?.positionId,
    })
    submitSuccess('add', data)
  }
}
const getPositionInfo = async () => {
  const { data } = await queryInterviewRecordById({
    id: paramsId.value,
  })
  fromDate.value = { ...data }
  releaseActiveAddress.value = {
    address: data.address,
    addressId: data.addressId,
  }
  currentDateTimes.value = valueOfToDate(fromDate.value.agreeTime, 'HH:mm')
  fromDate.value.agreeTime = valueOfToDate(fromDate.value.agreeTime, 'YYYY-MM-DD')
}
function initiateInit() {
  fromDate.value.receiveUserId = pageParams.value?.receiveUserId || null
  if (paramsId.value) {
    getPositionInfo()
  } else {
    fromDate.value.agreeTime = formatDateDay()
    fromDate.value.hrName = userIntel.value.trueName
    fromDate.value.hrPhone = userIntel.value.phone
  }
}
async function handleIMMessageSendSuccess(msg: AnyObject) {
  const { id: msgId = 0 } = msg
  const { interview_appointment_record_id: id } = initiateExtInfo.value
  await hrInterviewRecordUpdateById({
    id,
    msgId,
    msgJson: JSON.stringify(initiateExtInfo.value),
  })
  uni.navigateBack()
}
uni.$on(EMIT_EVENT.IM_MESSAGE_SEND_SUCCESS, handleIMMessageSendSuccess)
onMounted(() => {
  initiateInit()
})
onBeforeUnmount(() => {
  uni.$off(EMIT_EVENT.IM_MESSAGE_SEND_SUCCESS)
  releaseActiveAddress.value = {}
})
</script>

<style lang="scss" scoped>
:deep(.custom-class) {
  height: 200rpx !important;
  font-size: 28rpx !important;
  color: #333 !important;
}
:deep(.custom-textarea-container-class) {
  height: 100% !important;
  font-size: 28rpx !important;
  color: #333 !important;
}
.btn_fixed {
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 0rpx 40rpx 20rpx;
    margin-top: 30rpx;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 24rpx;
      font-weight: 600;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
  .btn_box_close {
    margin-top: 5rpx;
    .btn_bg_close {
      color: #fff;
      background: #ff6b6b;
    }
  }
}
</style>
