<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging
    layout-only
    safe-area-inset-bottom
    bottom-bg-color="#ffffff"
    :paging-style="pageStyle"
    paging-class="pay-paging"
  >
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          safe-area-inset-top
          fixed
          left-arrow
          custom-class="!bg-transparent px-30rpx"
          @click-left="handleBack"
        />
        <view
          class="h-400rpx relative px-30rpx bg-cover bg-center bg-no-repeat z--1"
          :style="{ backgroundImage: `url(${releasePost})` }"
        ></view>
      </wd-config-provider>
    </template>
    <wd-config-provider :themeVars="themeVars">
      <view class="center">
        <view class="w-400rpx">
          <wd-tabs
            v-model="payPropTypeActive"
            color="#A28064"
            inactive-color="#9E9E9E"
            line-width="134rpx"
            line-height="4rpx"
          >
            <wd-tab
              v-for="(item, key) in payPropTypeList"
              :key="`tab-${key}`"
              :title="`${item.name}`"
              :name="key"
            />
          </wd-tabs>
        </view>
      </view>
    </wd-config-provider>
    <scroll-view scroll-x class="whitespace-nowrap">
      <view class="flex flex-row gap-40rpx px-32rpx py-50rpx">
        <view
          class="relative flex flex-col items-center bg-white border-5rpx border-solid border-#e5e5e5 rounded-32rpx pb-62rpx flex-shrink-0"
          :class="{
            '!border-#A28064': selectedPayProp?.id === item.id,
          }"
          :style="{ width: 'calc((100vw - 144rpx) / 3)' }"
          v-for="(item, key) in filteredPayPropList"
          :key="`prop-${key}`"
          @click="selectedPayProp = item"
        >
          <view
            class="center w-70% h-38rpx text-22rpx text-white bg-#dcdada mt--1px"
            :class="{
              '!bg-#A28064': selectedPayProp?.id === item.id,
            }"
            style="clip-path: polygon(0% 0%, 100% 0%, 80% 100%, 20% 100%)"
          >
            {{ item.categoryName }}
          </view>
          <view class="text-#9E9E9E text-28rpx mt-20rpx relative line-through">
            ￥{{ divide(item.originalPrice, 100) }}
          </view>
          <view class="mt-24rpx">
            <text
              class="c-#9E9E9E text-26rpx"
              :class="{
                'text-#A28064': selectedPayProp?.id === item.id,
              }"
            >
              ￥
            </text>
            <text
              class="text-36rpx font-500 text-#333333"
              :class="{
                'text-#A28064': selectedPayProp?.id === item.id,
              }"
            >
              {{ divide(item.price, 100) }}
            </text>
          </view>
        </view>
      </view>
    </scroll-view>
    <template #bottom>
      <view
        class="bg-white shadow-[0rpx_8rpx_20rpx_0rpx_rgba(0,0,0,0.3)] px-24rpx pt-28rpx pb-38rpx flex items-center gap-40rpx"
      >
        <view class="c-#333333">
          <text class="text-28rpx">￥</text>
          <text class="text-56rpx">{{ divide(selectedPayProp?.price ?? 0, 100) }}</text>
        </view>
        <view
          class="rounded-16rpx bg-#2F2F2F h-112rpx w-full flex-1 relative flex items-center pl-46rpx"
        >
          <view
            class="absolute bottom-130rpx w-306rpx bg-white rounded-16rpx shadow-lg z-10 payment-popup overflow-hidden"
            :class="{ show: showPaymentMethod }"
          >
            <view class="flex flex-col gap-24rpx px-30rpx py-26rpx">
              <view
                class="flex items-center gap-26rpx"
                v-for="(paymentItem, index) in paymentMethods"
                :key="`payment-${index}`"
                @click="paymentItem.action"
              >
                <wd-icon :name="paymentItem.icon" size="50rpx" />
                <text class="text-28rpx c-#333333">{{ paymentItem.name }}</text>
              </view>
            </view>
          </view>
          <view class="flex items-center gap-38rpx w-full" @click="togglePaymentMethod">
            <view class="flex items-center c-#E4CC9C text-34rpx gap-20rpx">
              <text>¥{{ divide(selectedPayProp?.price ?? 0, 100) }}</text>
              <text>购买{{ selectedPayProp?.categoryName }}</text>
            </view>
            <view class="h-70rpx center border-l-1px border-l-solid border-l-#575757 flex-1">
              <wd-icon name="arrow-up" color="#E4CC9C" size="30rpx" />
            </view>
          </view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import { useToast, useMessage } from 'wot-design-uni'
import { PropCardType, PayPropId, EMIT_EVENT } from '@/enum'
import { PAY_PROP_TYPE_CONFIG } from '@/enum/payProp'
import { divide } from '@/utils'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { payPropQueryPropCategoryListByPosition } from '@/service/payProp'
import { payQueryDealStatus } from '@/service/pay'
import type { payPropQueryPropCategoryListByPositionInt } from '@/service/payProp/types'
import releasePost from '@/static/deepseek/business/release-post.png'
import aliPay from '@/paymentRelated/static/ali-pay.png'
import wechatPay from '@/paymentRelated/static/wechat-pay.png'
import balancePay from '@/paymentRelated/static/balance-pay.png'

interface PayPropType {
  id: readonly PayPropId[]
  name: string
}
interface PaymentMethod {
  icon: string
  name: string
  action: () => void
}
const DEFAULT_ID_MAP = { 0: 18, 1: 22 } as const
const toast = useToast()
const message = useMessage()
const { resetReleasePostModel } = useReleasePost()
const { paymentPrePay } = usePayment()
const { pageParams } = usePagePeriod()
const { pageStyle } = usePaging({
  style: {
    background: '#464646',
  },
})
const {
  bool: showPaymentMethod,
  setFalse: setShowPaymentMethod,
  toggle: togglePaymentMethod,
} = useBoolean()

const themeVars: ConfigProviderThemeVars = {
  navbarColor: '#ffffff',
  tabsNavLineBgColor: '#A28064',
}
const pollTimer = ref<number | null>(null)
const selectedPropId = ref<number | null>(null)
const payPropTypeActive = ref(0)
const payPropTypeList = ref<PayPropType[]>([
  {
    id: PAY_PROP_TYPE_CONFIG.BASIC.ids,
    name: PAY_PROP_TYPE_CONFIG.BASIC.name,
  },
  {
    id: PAY_PROP_TYPE_CONFIG.ADVANCED.ids,
    name: PAY_PROP_TYPE_CONFIG.ADVANCED.name,
  },
])
const payPropList = ref<payPropQueryPropCategoryListByPositionInt[]>([])
const paramsPositionId = computed(() => pageParams.value?.positionId)
const filteredPayPropList = computed(() => {
  const currentType = payPropTypeList.value[payPropTypeActive.value]
  if (!currentType) return []
  return payPropList.value.filter((item) => currentType.id.includes(item.id))
})
const selectedPayProp = computed({
  get() {
    const filtered = filteredPayPropList.value
    if (filtered.length === 0) return null
    let selectedItem: payPropQueryPropCategoryListByPositionInt | undefined
    if (selectedPropId.value) {
      selectedItem = filtered.find((item) => item.id === selectedPropId.value)
      if (selectedItem) return selectedItem
    }
    const defaultId = DEFAULT_ID_MAP[payPropTypeActive.value as keyof typeof DEFAULT_ID_MAP]
    if (defaultId) {
      const defaultItem = filtered.find((item) => item.id === defaultId)
      if (defaultItem) {
        return defaultItem
      }
    }
    return filtered[0] || null
  },
  set(value: payPropQueryPropCategoryListByPositionInt | null) {
    selectedPropId.value = value?.id || null
  },
})

async function payPropQueryPropCategoryListByPositionApi() {
  const { data } = await payPropQueryPropCategoryListByPosition({
    positionId: paramsPositionId.value,
    propId: PropCardType.RELEASE_POSITION,
  })
  payPropList.value = data
}

const paymentMethods = ref<PaymentMethod[]>([
  {
    icon: balancePay,
    name: '余额: 1999',
    action: () => handlePayment('balance'),
  },
  {
    icon: aliPay,
    name: '支付宝',
    action: () => handlePayment('alipay'),
  },
  {
    icon: wechatPay,
    name: '微信',
    action: () => handlePayment('wxpay'),
  },
])

const handlePayment = async (provider: 'balance' | 'alipay' | 'wxpay') => {
  setShowPaymentMethod()
  const payPass = provider === 'wxpay' ? 0 : 1
  if (provider === 'balance' || provider === 'alipay') {
    uni.showToast({
      title: '暂不支持该支付方式',
      icon: 'none',
    })
    return
  }
  toast.loading({
    msg: '支付中...',
    loadingType: 'outline',
    duration: 0,
    cover: true,
  })
  try {
    const outTradeNo = await paymentPrePay({
      provider,
      payPass,
      positionInfoId: paramsPositionId.value,
      propType: 0,
      propId: PropCardType.RELEASE_POSITION,
      propCategoryId: selectedPropId.value,
    })
    // 开始轮询支付状态
    await pollPaymentStatus(outTradeNo)
  } catch (error) {
    toast.close()
  }
}

/**
 * 轮询支付状态
 * @param outTradeNo 订单号
 */
const pollPaymentStatus = async (outTradeNo: string) => {
  const maxAttempts = 60
  const interval = 2000
  let attempts = 0
  const checkStatus = async (): Promise<void> => {
    attempts++
    try {
      const { data } = await payQueryDealStatus({
        outTradeNo,
      })
      if (data) {
        clearPollTimer()
        toast.close()
        toast.show('发布成功')
        setTimeout(() => {
          uni.$emit(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
          uni.navigateBack({
            delta: 2,
          })
        }, 1500)
        return
      }
      if (attempts < maxAttempts) {
        pollTimer.value = setTimeout(() => checkStatus(), interval)
      } else {
        clearPollTimer()
        toast.close()
        message
          .alert({
            title: '提示',
            msg: '支付状态确认超时，请到订单页面查看支付结果',
            closeOnClickModal: false,
            confirmButtonText: '知道了',
          })
          .then(() => {
            // TODO: 跳转到订单页面
          })
      }
    } catch (error) {
      if (attempts < maxAttempts) {
        pollTimer.value = setTimeout(() => checkStatus(), interval)
      } else {
        clearPollTimer()
        toast.close()
        toast.error({
          msg: '网络异常',
          duration: 2000,
        })
      }
    }
  }
  pollTimer.value = setTimeout(() => checkStatus(), 1000)
}

/**
 * 清除轮询定时器
 */
const clearPollTimer = () => {
  if (pollTimer.value) {
    clearTimeout(pollTimer.value)
    pollTimer.value = null
  }
}

onBeforeUnmount(() => {
  clearPollTimer()
})

const handleBack = () => {
  uni.navigateBack()
}
watch(
  selectedPayProp,
  (newValue) => {
    if (newValue?.id && selectedPropId.value !== newValue.id) {
      selectedPropId.value = newValue.id
    }
  },
  { immediate: true },
)

watch(payPropTypeActive, () => {
  selectedPropId.value = null
})
onMounted(async () => {
  await uni.$onLaunched
  await payPropQueryPropCategoryListByPositionApi()
})
</script>

<style lang="scss" scoped>
.pay-paging {
  :deep(.zp-view-super) {
    height: 100%;
    background: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
  }
}
:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__line {
    bottom: 8px;
  }
}
.payment-popup {
  max-height: 0;
  transition: max-height 0.3s ease-in;
  transform-origin: bottom;

  &.show {
    max-height: 300rpx;
  }
}
</style>
