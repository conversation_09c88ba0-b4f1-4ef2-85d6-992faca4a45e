<template>
  <view class="pageList-item flex items-start" @click="goDetail">
    <view class="pageList-item-left">
      <view class="c-#000 text-24rpx">
        {{ item.agreeTime.slice(5, 7) }}月{{ item.agreeTime.slice(8, 10) }}日
      </view>
      <view class="c-#000 text-34rpx text-right">{{ item.agreeTime.slice(11, 13) }}点</view>
    </view>
    <view class="pageList-item-right m-l-30rpx pageList-right p-l-40rpx p-b-40rpx">
      <view
        :class="item.status === 1 ? 'bg-#4399ff' : 'bg-#F2F2F2'"
        class="pageList-item-right-card relative"
      >
        <view class="flex items-center">
          <view class="w-90rpx">
            <image
              :src="item.headImgUrl"
              class="w-76rpx h-76rpx rounded-full"
              mode="aspectFill"
            ></image>
          </view>
          <view class="flex-1">
            <view class="flex items-center justify-between">
              <view
                :class="item.status === 1 ? 'c-#fff' : 'c-#000'"
                class="text-28rpx p-b-5rpx u-line-1 w-340rpx"
              >
                {{ truncateText(item.name, 8) }}
              </view>
            </view>

            <view class="flex justify-between">
              <view :class="item.status === 1 ? 'c-#fff' : 'c-#000'" class="text-28rpx">
                {{ item.positionName }}
              </view>
              <view :class="item.status === 1 ? 'c-#fff' : 'c-#000'" class="text-24rpx">
                {{ item.workSalaryStart
                }}{{ item.workSalaryEnd ? `- ${item.workSalaryEnd}/月` : '' }}
              </view>
            </view>
          </view>
        </view>
        <view v-if="item.status === 1" class="absolute top-[7rpx] right-[85rpx] z-100">
          <wd-img :height="15" :src="starIcon" :width="15" />
        </view>
        <view v-if="item.status === 1" class="absolute top-[0rpx] right-[-10rpx] z-100">
          <wd-img :height="35" :src="interview" :width="35" />
        </view>

        <view
          v-if="item.status === 2 || item.status === 3 || item.status === 4"
          class="c-#FF5B5B text-24rpx border-1rpx border-solid border-#FF5B5B rounded-[10rpx] px-10rpx absolute top-[30rpx] right-[20rpx] z-1000"
        >
          {{ item.status === 2 ? '已取消' : item.status === 3 ? '已取消' : '已取消' }}
        </view>
        <view v-if="item.status === 0">
          <view
            class="c-#4399FF text-24rpx border-1rpx border-solid border-#4399FF rounded-[10rpx] px-15rpx absolute top-[30rpx] right-[20rpx] z-1000"
          >
            待处理
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { truncateText } from '@/utils/util'
import starIcon from '@/sub_business/static/interview/star_icon.png'
import interview from '@/sub_business/static/interview/interview.png'

const props = defineProps<{
  item: any
}>()
const goDetail = () => {
  uni.navigateTo({
    url: `/sub_business/pages/interview/detail?id=${props.item.id}&toType=toPerson`,
  })
}
</script>
<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}

.pageList-right {
  border-left: 1rpx solid #cccaca;
}

.pageList-item-right-card {
  width: 494rpx;
  padding: 30rpx;
  border-radius: 20rpx;
}
</style>
