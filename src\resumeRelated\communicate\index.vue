<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar v-if="selectTag == 1" title="沟通过"></CustomNavBar>
    <CustomNavBar v-if="selectTag == 2" title="已投递"></CustomNavBar>
    <z-paging
      ref="pagingRef"
      v-model="groupedData"
      :fixed="false"
      :paging-style="pageStyle"
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
      safe-area-inset-bottom
      @query="queryList"
    >
      <view>
        <view class="pageList">
          <view class="page_list">
            <view v-for="(group, groupIndex) in groupedData" :key="groupIndex">
              <view class="content-data">
                {{ group.yearMonthDay }}
              </view>
              <view
                v-for="(item, index) in group.items"
                :key="index"
                class="page_flex p-t-40rpx"
                @click="goDetail(item.id, item.companyId)"
              >
                <view class="onlineRes">
                  <view class="my-jl-card p-b-30rpx">
                    <view class="flex-between p-l-40rpx p-r-40rpx">
                      <view class="flex-1 flex-c">
                        <view
                          :class="
                            item.hxUserInfoVO?.isOnline || item.activityStatus === 1
                              ? 'border-twinkle bg_left_icon_box'
                              : ''
                          "
                          class="m-r-10rpx h-100% flex items-center"
                        >
                          <image
                            :src="item.hrPositionUrl"
                            class="bg_left_icon"
                            mode="aspectFill"
                          ></image>
                        </view>

                        <view class="flex-1">
                          <view class="collect-conpany text-28rpx c-#555 m-b-10rpx">
                            {{ item.hrPositionName }}
                            <text v-if="item.hrPosition">·</text>
                            <text v-if="item.hrPosition">
                              {{ truncateText(item.hrPosition, 10) }}
                            </text>
                          </view>
                          <view class="card-name-subname subText line-18">
                            {{ item.name }}
                          </view>
                        </view>
                      </view>
                      <view>
                        <view
                          class="text-28rpx c-#888 text-r text-28rpx font-500 p-b-10rpx"
                          style="width: 200rpx"
                        >
                          <text>{{ item.workSalaryBegin }}</text>
                          <text v-if="item.workSalaryEnd">-</text>
                          <text v-if="item.workSalaryEnd">
                            {{ item.workSalaryEnd
                            }}{{
                              item?.salaryMonths === 12 ? '/月' : '·' + item.salaryMonths + '薪'
                            }}
                          </text>
                        </view>
                        <view class="page_right_flex flex-c" style="justify-content: right">
                          <wd-icon color="#999" name="location" size="12px"></wd-icon>
                          <view class="color-8 text-r text-24rpx p-b-10rpx">
                            {{ item.distanceMeters ? item.distanceMeters : item.districtName }}
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="my-jl-card-right flex-between p-t-10rpx p-l-40rpx p-r-40rpx">
                      <view class="page_right_flex-v w-40 flex-c items-baseline">
                        <wd-img :height="14" :src="imgPrent" :width="14" class="m-l-6rpx" />

                        <view
                          v-if="item.positionName"
                          class="c-#888 text-r text-24rpx p-l-15rpx line-clamp-1"
                        >
                          {{ truncateText(item.positionName, 10) }}
                        </view>
                      </view>
                      <view class="w-60 text-right">
                        <view
                          v-for="(subName, index) in item.positionKey"
                          :key="index"
                          class="my-jl-card-right-btn"
                        >
                          <view class="subText m-l-10rpx">
                            {{ subName }}
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { ditchThroughList, sendResumeList } from '@/interPost/resume'
import { getCustomBar } from '@/utils/storage'
import { ChatUIKit } from '@/ChatUIKit/index'
import imgPrent from '@/resumeRelated/img/imgPrent.png'
import { distanceHandle, truncateText } from '@/utils/util'
import { numberTokw } from '@/utils/common'

const appUserStore = ChatUIKit.appUserStore
defineOptions({
  name: 'Home',
})
const customBar = ref(null)
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    padding: '60rpx 0rpx 0rpx',
    marginTop: '40rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
  },
})

const groupedData = ref<any[]>([])
const params = reactive({
  entity: {},
  orderBy: {},
  size: pageInfo.size,
  page: pageInfo.page,
})
const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}
const groupByYearMonth = (data: any[]) => {
  if (!data || !Array.isArray(data)) return []

  const groups: Record<string, any[]> = {}

  data.forEach((item) => {
    if (!item.createTime) return

    const date = new Date(item.createTime)
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    const yearMonthDay = `${year}年${month}月${day}日`

    if (!groups[yearMonthDay]) {
      groups[yearMonthDay] = []
    }
    groups[yearMonthDay].push(item)
  })

  // Sort groups by date (newest first)
  const sortedGroups = Object.keys(groups).sort((a, b) => {
    const dateA = new Date(a.replace('年', '-').replace('月', '-').replace('日', ''))
    const dateB = new Date(b.replace('年', '-').replace('月', '-').replace('日', ''))
    return dateB.getTime() - dateA.getTime()
  })

  return sortedGroups.map((key) => ({
    yearMonthDay: key,
    items: groups[key],
  }))
}

const processListData = (list: any[]) => {
  if (!list) return []

  return list.map((item) => {
    try {
      const eleData = appUserStore.getUserInfoFromStore(item.hxUserInfoVO.username)
      console.log(eleData, item.hxUserInfoVO.username, 'eleData=====================')
      item.hxUserInfoVO.isOnline = !!eleData.isOnline
    } catch (error) {
      item.hxUserInfoVO.isOnline = false
    }
    return {
      ...item,
      positionKey: (() => {
        // 处理positionKey，确保正确分割
        let positionKeyValue = item.positionKey

        // 如果是数组，直接使用
        if (Array.isArray(positionKeyValue)) {
          return positionKeyValue.slice(0, 2)
        }

        // 如果是字符串，按逗号分割
        if (typeof positionKeyValue === 'string') {
          // 移除可能的空格
          positionKeyValue = positionKeyValue.trim()
          if (positionKeyValue) {
            return positionKeyValue
              .split(',')
              .map((item) => item.trim())
              .slice(0, 2)
          }
        }

        // 其他情况返回空数组
        return []
      })(),

      workSalaryBegin: item.workSalaryBegin === 0 ? '面议' : numberTokw(item.workSalaryBegin + ''),
      workSalaryEnd: item.workSalaryEnd === 0 ? '' : numberTokw(item.workSalaryEnd + ''),
      distanceMeters: item.distanceMeters ? distanceHandle(item.distanceMeters) : '',
      hrPositionUrl: item.hrPositionUrl
        ? item.hrPositionUrl
        : item.sex === 1
          ? '/static/header/hrheader1.png'
          : '/static/header/hrheader2.png',
    }
  })
}

const ditchThroughListFun = async () => {
  try {
    const res: any = await ditchThroughList(params)
    if (res.code === 0) {
      const processedData = processListData(res.data?.list || [])
      groupedData.value = groupByYearMonth(processedData)
      console.log(groupedData.value, 'groupedData.value==========')
      pagingRef.value.complete(groupedData.value)
    }
  } catch (error) {
    console.error('ditchThroughListFun error:', error)
    pagingRef.value.complete(false)
  }
}

const sendResumeListFun = async () => {
  try {
    const res: any = await sendResumeList(params)
    if (res.code === 0) {
      const processedData = processListData(res.data?.list || [])
      groupedData.value = groupByYearMonth(processedData)
      console.log(groupedData.value, 'groupedData.value==========')
      pagingRef.value.complete(groupedData.value)
    }
  } catch (error) {
    console.error('sendResumeListFun error:', error)
    pagingRef.value.complete(false)
  }
}

const queryList = async () => {
  if (selectTag.value === 1) {
    await ditchThroughListFun()
  } else {
    await sendResumeListFun()
  }
}

const selectTag = ref(1)
const list1 = ref([{ name: '本科' }])

onLoad(async (options) => {
  await nextTick()
  const subName = options.indexActive
  selectTag.value = Number(subName) || 1
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
}

.border-twinkle {
  position: relative;

  &::before {
    position: absolute;
    top: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    left: -2rpx;
    z-index: -1;
    content: '';
    background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
    border-radius: 50rpx;
    animation: twinkle 2s infinite;
  }
}

.bg_left_icon_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 65rpx;
  height: 65rpx;
  border: 3rpx solid #0ea500;
  border-radius: 50rpx;
}

.content-data {
  padding: 0rpx 40rpx 0rpx;
  font-size: 28rpx;
  line-height: 60rpx;
  color: #888;
  background: #f5f5f5;
}

.salary {
  color: #4399ff !important;
}

.content_search-p {
  padding: 30rpx 40rpx;
}

.bg_left_icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50rpx;
}

.page_left_1 {
  font-size: 22rpx !important;
  font-weight: 400;
  line-height: 44rpx;
  color: #888888;
}

.my-jl-card {
  .my-jl-card-left {
    justify-content: flex-start;
    width: 50%;

    .my-jl-card-left-header {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
    }

    .my-jl-card-left-item {
      margin-left: 10rpx;

      .card-name {
        font-weight: 500;
        color: #555;
      }
    }
  }

  .my-jl-card-right {
    flex: 1;
    flex-wrap: wrap;

    .my-jl-card-right-btn {
      display: flex;
      display: inline-block;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: right;
      padding: 2rpx 20rpx;
      margin: 10rpx 10rpx 10rpx 0rpx;
      background-color: #f3f3f3;
      border-radius: 10rpx;

      .pic {
        width: 20rpx;
        height: 20rpx;
      }
    }
  }

  .my-jl-card-cunstrct {
    line-height: 52rpx;

    .my-jl-card-cunstrct-img {
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 10rpx;

  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 152rpx;
    height: 64rpx;
    text-align: center;
    background: #fff4f4;
    border-radius: 20rpx 20rpx 20rpx 20rpx;

    &_icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 22rpx;
        font-weight: 400;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 20rpx;
        font-weight: 400;
        color: #999999;
      }
    }
  }
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 4rpx 40rpx;
    margin-top: 14rpx;
    margin-right: 22rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.page_list {
  box-sizing: border-box;
  width: 100%;
}

.onlineRes {
  border-bottom: 1rpx solid #e0e0e0;
}

.content_search_list_flex {
  box-sizing: border-box;
  display: flex !important;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
  padding: 0rpx 40rpx 40rpx;
}

.navigation-bar-betwween {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 60rpx;
  font-weight: 500;
  color: #000000;
}

.content_list_right {
  display: flex;
  flex-direction: row;
  align-items: center;

  .content_list_adress {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 24rpx;
    font-weight: 400;
    color: #555555;
  }

  .content_list_icon {
    display: flex;
    align-items: center;
    padding: 16rpx 10rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    ._img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}

.content_list_left_for {
  display: flex;
  flex-direction: row;
  padding-right: 50rpx;
}

.content_list_left_color {
  font-size: 28rpx;
  font-weight: 600;
  color: #000000;
}

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 10rpx 20rpx;
      white-space: nowrap;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_list_for {
        .content_list_border_1 {
          padding-left: 30rpx;
        }
      }
    }
  }
}

.select_border {
  padding: 11rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #333333;
  text-align: center;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.select_noBorder {
  font-size: 24rpx;
  line-height: 44rpx;
  color: #3e3e56;
  text-align: center;
}

.content_list_left_xian {
  width: 90rpx;
  height: 4rpx;
  padding-top: 1rpx;
  font-weight: bold;
  background: #4399ff;
  border-radius: 2rpx 2rpx 2rpx 2rpx;
}

.content_list_left_color1 {
  font-size: 28rpx;
  color: #888;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 10rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;

        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.content {
  width: 100%;
}

.start_ICON {
  ._icon {
    width: 56rpx;
    height: 56rpx;
  }
}
</style>
