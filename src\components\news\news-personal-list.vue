<template>
  <view class="flex flex-col gap-34rpx">
    <view
      v-for="(item, key) in displayList"
      :key="`news-list-${key}`"
      class="flex items-center gap-20rpx"
      @click="toChatPage(item.conversationId)"
    >
      <view :class="item?.isOnline ? 'border-twinkle bg_left_icon_box' : ''" class="size-90rpx">
        <wd-img :src="item?.avatar" height="100%" round width="100%" />
      </view>
      <view class="flex items-center flex-1 border-b border-#F0F0F0">
        <view class="flex flex-col gap-16rpx flex-1 pb-14rpx">
          <view class="flex items-center gap-4rpx">
            <view class="flex-1 line-clamp-1">
              <text class="c-#333333 text-28rpx font-500 mr-6rpx">
                {{ item?.nickname }}
              </text>
              <text class="c-#888888 text-24rpx font-500">
                {{
                  [getUserExt(item.ext).companyShortName, getUserExt(item.ext).hrPosition]
                    .filter(Boolean)
                    .join(' | ')
                }}
              </text>
            </view>
          </view>
          <text class="line-clamp-1 c-#888888 text-22rpx">
            {{ getLastTypeMessage(item.lastMessage as any) }}
          </text>
        </view>
        <view class="flex flex-col items-end gap-10rpx">
          <text class="c-#BABABA text-22rpx">
            {{
              formatSmartTime(
                item.lastMessage && 'time' in item.lastMessage ? item.lastMessage.time : '',
              )
            }}
          </text>
          <view v-if="!!item.unReadCount" class="bg-#FF3333 size-32rpx center border-rd-50%">
            <text class="text-20rpx c-#ffffff">
              {{ item.unReadCount > 99 ? '99+' : item.unReadCount }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CONVERSATION_MARKS } from '@/enum'
import { formatSmartTime } from '@/utils'
import type { ConversationWithUserInfo } from '@/hooks/common/useIMConversation'

type ConversationMarkType = (typeof CONVERSATION_MARKS)[keyof typeof CONVERSATION_MARKS]

interface propsInt {
  type: ConversationMarkType
}

const props = withDefaults(defineProps<propsInt>(), {
  type: CONVERSATION_MARKS.ALL,
})
const searchModel = defineModel<string>('search', {
  default: '',
  type: String,
})
const $emits = defineEmits<{
  (e: 'chat', id: string): void
}>()

const { conversationList, getLastTypeMessage, getConversationsByMark } = useIMConversation()

const markConversations = ref<ConversationWithUserInfo[]>([])

const displayList = computed(() => {
  let list =
    props.type === CONVERSATION_MARKS.ALL ? conversationList.value : markConversations.value
  if (searchModel.value.trim()) {
    list = list.filter((item) =>
      item?.nickname?.toLowerCase().includes(searchModel.value.toLowerCase()),
    )
  }
  return list
})

function getUserExt(ext: string): Api.IM.UserBusinessExtInfo {
  if (ext) {
    return JSON.parse(ext)
  }
  return {
    companyId: 0,
    companyName: '',
    companyShortName: '',
    hrPosition: '',
    hrUserId: 0,
  }
}

const loadMarkConversations = async () => {
  if (props.type === CONVERSATION_MARKS.ALL) return
  try {
    const conversations = await getConversationsByMark(props.type)
    markConversations.value = conversations
  } catch (error) {
    markConversations.value = []
  }
}
watch(() => props.type, loadMarkConversations, { immediate: true })
watch(
  conversationList,
  () => {
    if (props.type !== CONVERSATION_MARKS.ALL) {
      loadMarkConversations()
    }
  },
  { deep: true },
)
const toChatPage = (id: string) => {
  $emits('chat', id)
}
</script>

<style lang="scss" scoped>
//
.border-twinkle {
  position: relative;

  &::before {
    position: absolute;
    top: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    left: -2rpx;
    z-index: -1;
    content: '';
    background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
    border-radius: 50rpx;
    animation: twinkle 6s infinite;
  }
}

.bg_left_icon_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 86rpx;
  height: 86rpx;
  border: 3rpx solid #0ea500;
  border-radius: 50rpx;
}
</style>
