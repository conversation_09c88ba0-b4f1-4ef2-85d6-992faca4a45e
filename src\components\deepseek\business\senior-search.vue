<template>
  <z-paging :fixed="false" layout-only safe-area-inset-bottom>
    <wd-config-provider :themeVars="themeVars">
      <view class="px-32rpx pb-40rpx flex flex-col gap-56rpx">
        <view class="flex flex-col gap-44rpx">
          <view
            class="rounded-38rpx h-76rpx w-344rpx bg-#2F2F2F px-36rpx flex items-center"
            @click="handleSelectPost"
          >
            <text class="c-#E4CC9C text-28rpx flex-1 line-clamp-1">
              {{ releaseSeniorPostActivePost.positionName }}
            </text>
            <text
              :class="modelShow ? 'i-carbon-triangle-solid' : 'i-carbon-triangle-down-solid'"
              class="text-16rpx c-#E4CC9C"
            />
          </view>
          <!-- <view class="flex items-center flex-wrap gap-x-34rpx gap-y-24rpx">
            <view
              v-for="(item, key) in releaseActivePost.positionKeyList"
              :key="`keywords-${key}`"
              class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
            >
              <text class="text-24rpx c-#D3D3D3">{{ item }}</text>
            </view>
          </view> -->
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">期望薪资</text>
            <view class="h-132rpx rounded-38rpx bg-#2F2F2F flex items-center">
              <wd-picker
                ref="pickerPop"
                v-model="salaryValue"
                :column-change="onSalaryColumnChange"
                :columns="salaryColumns"
                :display-format="salaryDisplayFormat"
                custom-class="custom-class"
                label=""
                placeholder="开始薪资 - 结束薪资"
                @confirm="handleSalaryConfirm"
              />
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">求职状态</text>
            <view class="flex items-center flex-wrap gap-x-18rpx gap-y-24rpx">
              <view
                v-for="(item, key) in seekStatusOptions"
                :key="`seek-${key}`"
                :class="{ 'border-#FFCB62': seniorPostModel.seekStatus === item.value }"
                class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-25rpx"
                @tap="handleSelectSeekStatus(item)"
              >
                <text
                  :class="{ 'c-#FFCB62': seniorPostModel.seekStatus === item.value }"
                  class="text-24rpx c-#D3D3D3"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">年龄要求</text>
            <view class="h-132rpx rounded-38rpx bg-#2F2F2F flex items-center">
              <wd-picker
                ref="agePickerPop"
                v-model="ageValue"
                :column-change="onAgeColumnChange"
                :columns="ageColumns"
                :display-format="ageDisplayFormat"
                label=""
                placeholder="起始年龄 - 截止年龄"
                @cancel="handleAgeCancel"
                @close="handleAgeClose"
                @confirm="handleAgeConfirm"
              />
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">性别</text>
            <view class="grid grid-cols-3 gap-34rpx">
              <view
                v-for="(item, key) in genderOptions"
                :key="`gender-${key}`"
                :class="{ 'border-#FFCB62': seniorPostModel.gender === item.value }"
                class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
                @tap="handleSelectGender(item)"
              >
                <text
                  :class="{ 'c-#FFCB62': seniorPostModel.gender === item.value }"
                  class="text-24rpx c-#D3D3D3"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">学历要求</text>
            <view class="flex items-center flex-wrap gap-x-34rpx gap-y-24rpx">
              <view
                v-for="(item, key) in qualificationOptions"
                :key="`qualification-${key}`"
                :class="{ 'border-#FFCB62': seniorPostModel.qualification === item.value }"
                class="h-76rpx min-w-200rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
                @tap="handleSelectQualification(item)"
              >
                <text
                  :class="{ 'c-#FFCB62': seniorPostModel.qualification === item.value }"
                  class="text-24rpx c-#D3D3D3"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </wd-config-provider>
    <template #bottom>
      <view class="p-[36rpx_30rpx]">
        <wd-button
          :round="false"
          custom-class="!bg-#2F2F2F !h-112rpx w-full"
          @click="handleConfirm"
        >
          <text class="c-#E4CC9C text-28rpx">保存并筛选</text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { DICT_IDS, Gender, SALARY_RANGE } from '@/enum'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { DictOption } from '@/hooks/common/useDictionary'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import type { hrResumeSeniorPostModelInt } from '@/service/hrResume/types'

const $emit = defineEmits<{
  (e: 'search', model: hrResumeSeniorPostModelInt): void
  (e: 'selectPost'): void
}>()
const modelShow = defineModel('show', {
  type: Boolean,
  default: false,
})
const { getDictOptions } = useDictionary()
const { releaseSeniorPostActivePost } = useReleasePost()
const themeVars: ConfigProviderThemeVars = {
  inputPlaceholderColor: '#FFCB62',
  inputColor: '#FFCB62',
  inputBg: 'transparent',
}
const seniorPostModel = ref<hrResumeSeniorPostModelInt>({})
const qualificationOptions = ref<DictOption[]>([])
const seekStatusOptions = ref<DictOption[]>([])
const genderOptions = ref<DictOption[]>([
  { value: Gender.UNSET, text: '不限' },
  { value: Gender.MALE, text: '男' },
  { value: Gender.FEMALE, text: '女' },
])

const salaryExpectationStart = ref<any>('') // 薪资开始
const salaryExpectationEnd = ref<any>('') // 薪资结束

const ageBegin = ref<any>('') // 年龄开始
const ageEnd = ref<any>('') // 年龄结束

const salaryColumns = ref([
  Object.keys(SALARY_RANGE).map((item) => ({ label: item, value: item })),
  SALARY_RANGE['面议'].map((item) => ({ label: item, value: item })),
])

const salaryValue = ref([])

// 年龄数据：不限 + 16-65岁
const ageColumns = ref([
  [
    { label: '不限', value: '不限' },
    ...Array.from({ length: 50 }, (_, i) => ({ label: `${i + 16}岁`, value: i + 16 })),
  ],
  [
    { label: '不限', value: '不限' },
    ...Array.from({ length: 50 }, (_, i) => ({ label: `${i + 16}岁`, value: i + 16 })),
  ],
])

const ageValue = ref([]) // 默认选择第一个选项（不限）

// 修改列变化处理
const onSalaryColumnChange = (picker, values, columnIndex, resolve) => {
  if (columnIndex === 0) {
    const selected = values[0]?.value || '面议'
    if (selected === '面议') {
      picker.setColumnData(1, [{ label: '面议', value: '面议' }])
    } else {
      picker.setColumnData(
        1,
        SALARY_RANGE[selected].map((item: any) => ({ label: item, value: item })),
      )
    }
    resolve()
  }
}

const salaryDisplayFormat = (items: any) => {
  if (items[0].label === '面议' && items[1].label === '面议') {
    return '面议'
  }
  return items.map((item: any) => item.label).join(' - ')
}

const onAgeColumnChange = (picker: any, values: any, columnIndex: any, resolve: any) => {
  if (columnIndex === 0) {
    const selectedAge = values[0]?.value
    if (selectedAge === '不限') {
      // 如果选择"不限"，第二列也显示"不限"选项
      picker.setColumnData(1, [
        { label: '不限', value: '不限' },
        ...Array.from({ length: 50 }, (_, i) => ({ label: `${i + 16}岁`, value: i + 16 })),
      ])
    } else {
      // 如果选择具体年龄，第二列显示大于该年龄的选项
      const startAge = typeof selectedAge === 'number' ? selectedAge : 16
      const endAgeOptions = Array.from({ length: 65 - startAge }, (_, i) => ({
        label: `${startAge + i + 1}岁`,
        value: startAge + i + 1,
      }))
      picker.setColumnData(1, endAgeOptions)
    }
    resolve()
  }
}

const ageDisplayFormat = (items: any) => {
  if (items[0].label === '不限' && items[1].label === '不限') {
    return '不限'
  }
  if (items[0].label === '不限') {
    return `不限-${items[1].label}`
  }
  if (items[1].label === '不限') {
    return `${items[0].label}-不限`
  }
  return items.map((item: any) => item.label).join(' - ')
}

const handleSalaryConfirm = ({ value }) => {
  if (value[0].indexOf('k') !== -1) {
    salaryExpectationStart.value = value[0].replace('k', '000')
    salaryExpectationEnd.value = value[1].replace('k', '000')

    seniorPostModel.value.salaryExpectationStart = salaryExpectationStart.value
    seniorPostModel.value.salaryExpectationEnd = salaryExpectationEnd.value
  } else {
    salaryExpectationStart.value = 0
    salaryExpectationEnd.value = 0
  }

  console.log(salaryExpectationStart.value, salaryExpectationEnd.value, 'value====')
}

// 处理年龄确认事件
const handleAgeConfirm = ({ value }) => {
  const startAge = value[0] === '不限' ? null : value[0]
  const endAge = value[1] === '不限' ? null : value[1]

  ageBegin.value = startAge // 开始年龄
  ageEnd.value = endAge // 结束年龄

  seniorPostModel.value.ageBegin = startAge
  seniorPostModel.value.ageEnd = endAge

  console.log(ageBegin.value, ageEnd.value, 'age====')
}

const handleAgeCancel = () => {
  console.log('年龄选择器已取消')
}

const handleAgeClose = () => {
  console.log('年龄选择器已关闭')
}

const fetchWorkEducationalOptions = async () => {
  const dictData = await getDictOptions(DICT_IDS.EDUCATION_B)
  qualificationOptions.value = dictData
}
const fetchWorkStatusOptions = async () => {
  const dictData = await getDictOptions(DICT_IDS.SEEK_STATUS)
  seekStatusOptions.value = dictData
}

function handleSelectQualification(item: DictOption) {
  seniorPostModel.value.qualification =
    seniorPostModel.value.qualification === item.value ? undefined : (item.value as number)
}

function handleSelectGender(item: DictOption) {
  seniorPostModel.value.gender =
    seniorPostModel.value.gender === item.value ? undefined : (item.value as number)
}

function handleSelectSeekStatus(item: DictOption) {
  seniorPostModel.value.seekStatus =
    seniorPostModel.value.seekStatus === item.value ? undefined : (item.value as number)
}

function handleSelectPost() {
  $emit('selectPost')
}

function handleConfirm() {
  const filteredModel = Object.fromEntries(
    Object.entries(seniorPostModel.value).filter(
      ([_, value]) =>
        value !== undefined && value !== null && !(typeof value === 'string' && value === ''),
    ),
  )
  const isHaveModel = Object.keys(filteredModel).length
  if (!isHaveModel) {
    uni.showToast({
      title: '请先选择筛选条件',
      icon: 'none',
    })
    return
  }
  if (seniorPostModel.value.salaryExpectationStart && seniorPostModel.value.salaryExpectationEnd) {
    const startSalary = Number(seniorPostModel.value.salaryExpectationStart)
    const endSalary = Number(seniorPostModel.value.salaryExpectationEnd)
    if (startSalary >= endSalary) {
      uni.showToast({
        title: '开始薪资必须小于结束薪资',
        icon: 'none',
      })
      return
    }
  }
  if (seniorPostModel.value.ageBegin && seniorPostModel.value.ageEnd) {
    const startAge = Number(seniorPostModel.value.ageBegin)
    const endAge = Number(seniorPostModel.value.ageEnd)
    if (startAge >= endAge) {
      uni.showToast({
        title: '起始年龄必须小于截止年龄',
        icon: 'none',
      })
      return
    }
  }
  $emit('search', filteredModel)
}

onMounted(async () => {
  await uni.$onLaunched
  fetchWorkEducationalOptions()
  fetchWorkStatusOptions()
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  .wd-input__placeholder {
    text-align: center;
  }
}

:deep(.zp-view-super) {
  margin: 0 !important;
}

:deep(.zp-paging-container-content) {
  height: auto !important;
}

// 修复 picker 弹窗关闭后底部残留内容的问题
// 简化的解决方案：只处理动画结束状态
:deep(.wd-transition) {
  &.wd-slide-up-leave-to,
  &.wd-fade-leave-to {
    display: none !important;
    pointer-events: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
}

:deep(.wd-picker) {
  width: 100%;
  height: 100%;

  .wd-picker__field {
    height: 100%;

    .wd-picker__cell {
      width: 100%;
      height: 100%;
      background: transparent;

      .wd-picker__body {
        height: 100%;

        .wd-picker__value-wraper {
          height: 100%;

          .wd-picker__value {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 26rpx;
            font-weight: normal !important;
            color: #ffcb62;
          }
        }

        .wd-icon {
          display: none;
        }
      }
    }
  }
}
</style>
