<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="面试"></CustomNavBar>
    <view class="page-time flex items-center p-l-40rpx p-r-40rpx p-t-25rpx">
      <view class="text-100rpx font-600 c-#000">{{ currentDate.slice(5, 7) }}</view>
      <view>
        <view class="text-32rpx c-#555">月</view>
        <view class="text-32rpx c-#555">{{ currentDate.slice(0, 4) }}年</view>
      </view>
    </view>

    <z-paging
      ref="pagingRef"
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 180rpx)` }"
    >
      <template #top>
        <view class="m-l-40rpx m-r-40rpx">
          <scroll-view
            ref="dateScrollRef"
            scroll-x
            class="white-space-nowrap w-[calc(100% - 80rpx)]"
            :scroll-left="scrollLeft"
          >
            <view class="gap-35rpx flex items-center p-b-60rpx">
              <view
                v-for="(item, index) in monthDays"
                :key="index"
                :id="'date-' + index"
                class="text-28rpx text-center relative"
                :class="selectedIndex === index ? 'date-selected' : ''"
                style="min-width: 60rpx; padding: 20rpx 0; cursor: pointer"
                @click="selectDate(index, item)"
              >
                <view>{{ item.week }}</view>
                <view>{{ item.date.slice(8, 10) }}</view>
                <view
                  v-if="item.isInterview"
                  class="w-10rpx h-10rpx bg-#ff0000 rounded-50 m-auto m-t-6rpx"
                ></view>
                <view v-if="selectedIndex === index && countInterviewList.length === 1">
                  <view
                    v-for="(item, index) in countInterviewList"
                    :key="index"
                    class="absolute bottom-[-20rpx] right-[25rpx]"
                  >
                    <wd-img
                      :src="item.userUrl"
                      :width="16"
                      :height="16"
                      mode="aspectFill"
                      round
                    ></wd-img>
                  </view>
                </view>
                <view
                  v-if="selectedIndex === index && countInterviewList.length === 2"
                  class="bg-#ffffff"
                >
                  <view
                    v-for="(item, index) in countInterviewList"
                    :key="index"
                    class="absolute bottom-[-25rpx]"
                    :style="{
                      right: `${index * 25 + 10 - index * 3}rpx`,
                      zIndex: `${1000 - index}`,
                    }"
                  >
                    <wd-img
                      class="border-2rpx border-solid border-#ffffff rounded-50"
                      :src="item.userUrl"
                      :width="16"
                      :height="16"
                      mode="aspectFill"
                      round
                    ></wd-img>
                  </view>
                </view>
                <view v-if="selectedIndex === index && countInterviewList.length === 3">
                  <view
                    v-for="(item, index) in countInterviewList"
                    :key="index"
                    class="absolute bottom-[-20rpx]"
                    :style="{
                      right: `${index * 25 + 10 - index * 3}rpx`,
                      zIndex: `${1000 - index}`,
                    }"
                  >
                    <wd-img
                      class="border-2rpx border-solid border-#888 rounded-50"
                      :src="item.userUrl"
                      :width="16"
                      :height="16"
                      mode="aspectFill"
                      round
                    ></wd-img>
                  </view>
                </view>
                <view
                  class="c-#000 text-20rpx absolute bottom-[-40rpx] right-[20rpx]"
                  v-if="selectedIndex === index && countInterview"
                >
                  {{ countInterview }}位
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </template>

      <view class="pageList">
        <view v-if="pageData.length > 0">
          <interviewList
            v-for="(item, index) in pageData"
            :key="index"
            :item="item"
            @tap="handleToPreviewDetail(item)"
          />
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { CommonUtil } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { hrInterviewRecordList } from '@/service/hrBiographical'
import interviewList from '@/sub_business/components/interviewList.vue'
import { getCustomBar } from '@/utils/storage'
import { getMonthDays, formatDateDay, numberTokw } from '@/utils/common'
import { queryTimeList } from '@/service/userInterviewRecord'

const { defaultRoleLogo } = useDefault()
const { pagingRef, pageData, pageStyle, pageInfo, pageSetInfo } = usePaging({
  style: {
    padding: '60rpx 0rpx 0rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    marginTop: '20rpx',
  },
})
const countInterview = ref(null)
const countInterviewList = ref([])
// 当前的月份对应的日期和星期
const monthDays = ref([])
// 当前日期
const currentDate = ref('')
const customBar = ref(null)
// 新增：高亮选中索引
const selectedIndex = ref(0)
const scrollToId = computed(() => 'date-' + selectedIndex.value)
const dateScrollRef = ref()
const scrollLeft = ref(0)
const isInitialized = ref(false)

function scrollDateToCenter(idx) {
  if (!dateScrollRef.value || idx < 0 || idx >= monthDays.value.length) {
    console.log('scrollDateToCenter: 参数无效', { idx, length: monthDays.value.length })
    return
  }

  // 使用更可靠的查询方式
  const query = uni.createSelectorQuery()
  query.select(`#date-${idx}`).boundingClientRect()
  query.select('.white-space-nowrap').boundingClientRect()
  query.exec((res) => {
    console.log('scrollDateToCenter: 查询结果', res)

    const itemRect = res[0]
    const scrollRect = res[1]

    if (itemRect && scrollRect) {
      // 计算需要滚动的距离，让选中项居中
      const targetScrollLeft =
        itemRect.left - scrollRect.left - (scrollRect.width - itemRect.width) / 2

      console.log('scrollDateToCenter: 计算滚动距离', {
        itemRect,
        scrollRect,
        targetScrollLeft,
      })

      // 直接设置滚动位置，让选中项居中
      scrollLeft.value = Math.max(0, targetScrollLeft)
    } else {
      // 备用方案：使用简单的计算方式
      console.log('scrollDateToCenter: 使用备用方案')
      const itemWidth = 80 // 每个日期项的宽度
      const gap = 60 // 间距
      const totalItemWidth = itemWidth + gap
      const targetScrollLeft = idx * totalItemWidth - 100 // 减去一些偏移量
      scrollLeft.value = Math.max(0, targetScrollLeft)
    }
  })
}
// 跳转详情页
const handleToPreviewDetail = (item) => {
  uni.navigateTo({
    url: '/sub_business/pages/interview/detail?id=' + item.id + '&toType=toBusiness',
  })
}
const selectDate = async (idx, item) => {
  selectedIndex.value = idx
  currentDate.value = item.date

  // 清空之前的面试数据，准备加载新日期的数据
  countInterview.value = 0
  countInterviewList.value = []

  await getInterviewList()
  // scrollDateToCenter(idx)
}

// 监听monthDays，数据变化后自动高亮今日并居中
watch(
  monthDays,
  (val) => {
    if (val && val.length > 0) {
      const todayIdx = val.findIndex((item) => item.isToday)
      console.log('watch monthDays: 找到今日索引', { todayIdx, total: val.length })

      // 只在初始化时执行滚动，避免重复滚动
      if (!isInitialized.value) {
        isInitialized.value = true
        // 设置选中状态并立即滚动到今日
        selectedIndex.value = todayIdx !== -1 ? todayIdx : 0
        console.log('初始化：设置选中索引', selectedIndex.value)

        // 立即滚动到今日，无需延迟
        nextTick(() => {
          console.log('开始滚动到今日')
          scrollDateToCenter(selectedIndex.value)
        })
      } else {
        // 非初始化时，直接设置选中状态
        selectedIndex.value = todayIdx !== -1 ? todayIdx : 0
        console.log('非初始化：设置选中索引', selectedIndex.value)
      }
    }
  },
  { immediate: true },
)
// 历史免滤
const goHistory = () => {
  uni.navigateTo({
    url: '/resumeRelated/interview/WaitMeeting',
  })
}
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await hrInterviewRecordList({
    entity: {
      createTime: currentDate.value,
    },
    orderBy: {},
    page: pageInfo.page,
    size: pageInfo.size,
  })
  console.log(res, 'res==========')
  if (res.code === 0) {
    res.data.list.forEach((item) => {
      item.salaryStart = item.salaryStart === 0 ? '面议' : numberTokw(item.salaryStart + '')
      item.salaryEnd = item.salaryEnd === 0 ? '' : numberTokw(item.salaryEnd + '')
      item.userUrl = defaultRoleLogo(item.sex, item.userUrl)
    })

    // 更新面试数据
    const confirmedInterviews = res.data.list.filter((item) => item.status === 1)
    countInterview.value = confirmedInterviews.length
    countInterviewList.value = confirmedInterviews.slice(0, 3) // 最多显示3个

    pagingRef.value.complete(res.data.list)
  } else {
    // 如果请求失败，清空面试数据
    countInterview.value = 0
    countInterviewList.value = []
    pagingRef.value.complete([])
  }
}
const getInterviewList = async () => {
  await pagingRef.value.reload()
}
uni.$on('refreshInterviewList', async () => {
  await getInterviewList()
  await queryTimeListData()
})
onUnload(async () => {
  uni.$off('refreshInterviewList')
})
const queryTimeListData = async () => {
  const startTime = monthDays.value.slice(selectedIndex.value, selectedIndex.value + 1)
  const endTime = monthDays.value.slice(monthDays.value.length - 1, monthDays.value.length)
  const res: any = await queryTimeList({
    startTime: startTime[0].date + ' 00:00:00',
    endTime: endTime[0].date + ' 23:59:59',
  })
  if (res.code === 0) {
    monthDays.value.forEach((item) => {
      res.data.forEach((ele) => {
        if (item.date === ele.dates) {
          item.isInterview = true
        }
      })
    })
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  monthDays.value = getMonthDays()
  currentDate.value = formatDateDay()
  console.log(monthDays.value, 'monthDays.value==========')
  // 确保选中当天并加载当天的面试数据
  const todayIdx = monthDays.value.findIndex((item) => item.isToday)
  if (todayIdx !== -1) {
    selectedIndex.value = todayIdx
  }
  await queryTimeListData()
  await getInterviewList()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}
.date-selected {
  min-width: 80rpx !important;
  color: #fff !important;
  background: #ff7648 !important;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
</style>
