<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="accountLogin bg-img">
    <CustomNavBar>
      <template #left>
        <wd-icon name="arrow-left" size="20px" color="#000" @click="goBack" />
      </template>
      <template #right>
        <wd-img :src="identitySwitchingImg" width="45rpx" height="45rpx" @click="changeIdentFun" />
      </template>
    </CustomNavBar>
    <view class="containner-page">
      <view class="containner-title">在职证明</view>
      <view class="m-b-40rpx text-28rpx">
        请上传"
        <text class="text-28rpx font-w-500">{{ params.companyName }}</text>
        "的在职证明，需加盖公章
      </view>

      <view class="containner-group">
        <image class="containner-group-img" :src="pic"></image>
      </view>
      <view class="downLoad" @click="downloadTemplateFun">下载模版</view>
      <view class="flex-c containner-group-text" @click="upLoadImg">
        <wd-icon name="photo" size="20px" color="#000"></wd-icon>
        <view class="text-24rpx m-l-10rpx">上传/拍摄</view>
      </view>

      <view class="containner-subText">
        <view class="containner-subText-name">注意事项:</view>
        <view class="containner-subText-name">1.下载在职证明模版</view>
        <view class="containner-subText-name">2.打印并加盖所在公司公章</view>
        <view class="containner-subText-name">3.在职证明内容包含贵公司授权您的招聘信息</view>
        <view class="containner-subText-name">4.在职证明落款日期在30日以内</view>
        <view class="containner-subText-name">为方便您填写证明，系统将为您自动填充已实名信息</view>
      </view>

      <view class="btn_fixed" @click="submit">
        <view class="btn_box">
          <view class="btn_bg">下一步</view>
        </view>
      </view>
    </view>
  </view>
  <canvas
    canvas-id="a4Template"
    style="position: absolute; top: -9999px; left: -9999px; width: 2480px; height: 3508px"
  ></canvas>
</template>

<script setup lang="ts">
import { commitJobRelation } from '@/interPost/company'
import { useUpload } from 'wot-design-uni'
import imgOne from '@/loginSetting/img/<EMAIL>'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
import { compressImage } from '@/utils/compressImage'
import { queryApplyInfo } from '@/service/companyIn'
const { changeIdent } = useChangeIdent()
const { newInfoStepPage } = useNewInfoAll()
const { getToken } = useUserInfo()
const { startUpload, chooseFile, UPLOAD_STATUS } = useUpload()
const { downloadTemplate } = useCanvasImg()
const { logoutBtn } = useLogout()
// 表单
const fromData = ref({
  employmentCertificateId: null,
})
// 状态
const isSwitching = ref(false)
const params = ref({})
// 图片
const pic = ref(imgOne)
const source = ref('')
// 绘制canvas
const goBack = () => {
  if (source.value === 'myInfo') {
    uni.navigateBack()
  } else {
    logoutBtn()
  }
}
// 图片列表
const fileList = ref([])
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
// 图片上传路径
const baseUrl =
  import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThumPrivate'
// 初始化
// 切换身份
const changeIdentFun = async () => {
  if (isSwitching.value) return
  isSwitching.value = true
  try {
    await changeIdent()
    isSwitching.value = false
  } catch (error) {
    isSwitching.value = false
  }
}
// 获取公司名字
const getCompanyName = async () => {
  const res: any = await queryApplyInfo()
  if (res.code === 0) {
    params.value = res.data
  }
}
onLoad((options) => {
  source.value = options.source || ''
  console.log(options, 'options=============')
})
onMounted(async () => {
  await uni.$onLaunched
  await getCompanyName()
})
// 开始上传
const upLoadImg = async () => {
  const files = await chooseFile({
    accept: 'image',
    multiple: false,
    maxCount: 1,
  })
  if (files[0].path) {
    // 1. 先压缩
    const compressed = await compressImage(
      {
        url: files[0].path,
        size: files[0].size,
        name: files[0].name,
        type: files[0].type,
        raw: files[0], // 直接传ChooseFile对象，兼容H5/APP
      },
      {
        quality: 0.8,
        maxWidth: 2000,
        maxHeight: 2000,
        maxSize: 20 * 1024 * 1024, // 20MB
      },
    )
    // 2. 校验大小
    if (compressed.size && compressed.size > 20 * 1024 * 1024) {
      uni.showToast({
        title: '图片大于20MB，请重新选择',
        icon: 'none',
      })
      return
    }
    // 3. 展示压缩后图片
    pic.value = compressed.url

    // 4. 上传压缩后的图片
    // H5端如果是dataURL，需要转成blob再上传
    let uploadFileObj: any = {
      url: compressed.url,
      name: compressed.name,
      type: compressed.type,
      size: compressed.size,
    }
    if (compressed.url && compressed.url.startsWith('data:')) {
      // dataURL转blob
      const arr = compressed.url.split(',')
      const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg'
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      const blob = new Blob([u8arr], { type: mime })
      uploadFileObj = {
        ...uploadFileObj,
        file: blob,
      }
    }
    await startUpload(uploadFileObj, {
      action: baseUrl,
      name: 'file',
      header: { token: getToken() },
      statusCode: 200,
      onSuccess(resImg) {
        try {
          const res = JSON.parse(resImg.data)
          if (res.code === 0) {
            fromData.value.employmentCertificateId = res.data[0].fileId
          } else {
            uni.showToast({
              title: '上传失败',
              icon: 'none',
              duration: 3000,
            })
          }
        } catch (e) {
          uni.showToast({
            title: '上传失败',
            icon: 'none',
            duration: 3000,
          })
        }
      },
      onError() {
        uni.showToast({
          title: '上传失败',
          icon: 'none',
          duration: 3000,
        })
      },
    })
  }
}
// 生成A4纸大小的canvas图片并保存到相册
const downloadTemplateFun = async () => {
  downloadTemplate(
    params.value?.trueName,
    params.value?.idCard,
    params.value?.companyName,
    params.value?.hrPosition,
  )
}
const submit = async () => {
  if (!fromData.value.employmentCertificateId) {
    uni.showToast({
      title: '请上传在职证明',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await commitJobRelation(fromData.value)
  if (res.code === 0) {
    if (source.value === 'myInfo') {
      uni.navigateBack()
    } else {
      newInfoStepPage(false, 1)
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
</script>

<style lang="scss" scoped>
.containner-page {
  padding: 40rpx;
  .containner-title {
    margin-bottom: 20rpx;
    font-size: 60rpx;
    font-weight: 600;
  }
  .containner-group {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20rpx;
    .containner-group-img {
      width: 320rpx;
      height: 400rpx;
    }
  }
  .downLoad {
    margin-bottom: 20rpx;
    color: rgba(3, 112, 255, 1);
    text-align: center;
  }
  .containner-group-text {
    justify-content: center;
    width: 300rpx;
    padding: 20rpx 0rpx;
    margin: auto;
    background-color: #fff;
    border-radius: 30rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
  }
  .containner-card {
    padding: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
    .sb-name {
      color: rgba(136, 136, 136, 1);
    }
    .sb-name-t {
      color: rgba(51, 51, 51, 1);
    }
  }
  .containner-subText {
    margin-top: 30rpx;
    .containner-subText-name {
      font-size: 26rpx;
      color: rgba(136, 136, 136, 1);
    }
  }
  .btn_fixed {
    position: fixed;
    right: 60rpx;
    bottom: 100rpx;
    left: 60rpx;
    box-sizing: border-box;
    width: calc(100% - 120rpx);
    .btn_box {
      .btn_bg {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 30rpx 0rpx;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
        border-radius: 14px 14px 14px 14px;
      }
    }
  }
}
</style>
