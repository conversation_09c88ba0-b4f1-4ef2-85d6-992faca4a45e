<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '添加常用语',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #right>
            <text class="c-#333333 text-28rpx" @click="handleSave">保存</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
    <view class="flex flex-col gap-30rpx px-50rpx">
      <view
        class="rounded-20rpx overflow-hidden mt-44rpx shadow-[0rpx_8rpx_29rpx_0rpx_rgba(0,0,0,0.1)]"
      >
        <wd-textarea
          v-model="phrasesCommonActive.content"
          placeholder="请输入常用语"
          :maxlength="200"
          clearable
          show-word-limit
        />
      </view>
      <text class="c-#888888 text-24rpx">备注:请勿输入QQ、微信、手机等联系方式或广告信息</text>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, type ConfigProviderThemeVars } from 'wot-design-uni'
import { EMIT_EVENT } from '@/enum'
import { sysUserCommonPhraseAdd, sysUserCommonPhraseUpdate } from '@/service/sysUserCommonPhrase'
import { usePhrases } from '@/sub_common/hooks/usePhrases'

const toast = useToast()
const { phrasesCommonActive } = usePhrases()
const { pageParams } = usePagePeriod()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: ' transparent',
  navbarArrowSize: '30rpx',
}
const phrasesIsEdit = computed(() => pageParams.value.type === 'edit')

function handleClickLeft() {
  uni.navigateBack()
}
const handleSave = CommonUtil.debounce(async () => {
  if (!phrasesCommonActive.value.content) {
    toast.show('请输入常用语')
    return
  }
  uni.showLoading({
    title: phrasesIsEdit.value ? '修改中...' : '添加中...',
    mask: true,
  })
  if (phrasesIsEdit.value && phrasesCommonActive.value.id) {
    await sysUserCommonPhraseUpdate({
      id: phrasesCommonActive.value.id,
      content: phrasesCommonActive.value.content,
      sortNo: phrasesCommonActive.value.sortNo ?? 0,
      isDefault: phrasesCommonActive.value.isDefault ?? 0,
      systemDefaultKey: phrasesCommonActive.value?.systemDefaultKey ?? null,
    })
  } else {
    await sysUserCommonPhraseAdd({
      content: phrasesCommonActive.value.content,
      systemDefaultKey: phrasesCommonActive.value?.systemDefaultKey ?? null,
    })
  }
  setTimeout(() => {
    uni.hideLoading()
    uni.$emit(EMIT_EVENT.REFRESH_COMMON_PHRASES)
    uni.navigateBack()
  }, 1000)
}, 300)
</script>

<style lang="scss" scoped>
:deep(.wd-textarea) {
  .wd-textarea__inner {
    height: 550rpx;
  }
}
</style>
