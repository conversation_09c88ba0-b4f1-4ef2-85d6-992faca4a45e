import NP from 'number-precision'

// 配置 number-precision
NP.enableBoundaryChecking(false)

/**
 * 输入验证和转换
 */
function validateAndConvert(value: number | string): number {
  // 处理 null、undefined 和空字符串
  if (value === null || value === undefined || value === '') {
    throw new Error(`无效的数字: ${value}`)
  }

  const num = Number(value)
  if (isNaN(num) || !isFinite(num)) {
    throw new Error(`无效的数字: ${value}`)
  }

  // 检查数值范围（防止超出 JavaScript 安全整数范围）
  if (Math.abs(num) > Number.MAX_SAFE_INTEGER) {
    console.warn(`数值 ${num} 可能超出安全范围，计算结果可能不准确`)
  }

  return num
}

/**
 * 验证小数位参数
 */
function validateDecimal(decimal: number): number {
  if (!Number.isInteger(decimal) || decimal < 0 || decimal > 20) {
    throw new Error(`小数位数必须是0-20之间的整数: ${decimal}`)
  }
  return decimal
}

/**
 * 精确加法
 */
export function add(a: number | string, b: number | string): number {
  return NP.plus(validateAndConvert(a), validateAndConvert(b))
}

/**
 * 精确减法
 */
export function subtract(a: number | string, b: number | string): number {
  return NP.minus(validateAndConvert(a), validateAndConvert(b))
}

/**
 * 精确乘法
 */
export function multiply(a: number | string, b: number | string): number {
  return NP.times(validateAndConvert(a), validateAndConvert(b))
}

/**
 * 精确除法
 */
export function divide(a: number | string, b: number | string): number {
  const divisor = validateAndConvert(b)
  if (divisor === 0) {
    throw new Error('除数不能为0')
  }
  return NP.divide(validateAndConvert(a), divisor)
}

/**
 * 四舍五入到指定小数位
 */
export function round(num: number | string, decimal: number = 2): number {
  return NP.round(validateAndConvert(num), validateDecimal(decimal))
}

/**
 * 向上取整到指定小数位
 */
export function ceil(num: number | string, decimal: number = 2): number {
  const value = validateAndConvert(num)
  const validDecimal = validateDecimal(decimal)
  const factor = Math.pow(10, validDecimal)
  return NP.divide(Math.ceil(NP.times(value, factor)), factor)
}

/**
 * 向下取整到指定小数位
 */
export function floor(num: number | string, decimal: number = 2): number {
  const value = validateAndConvert(num)
  const validDecimal = validateDecimal(decimal)
  const factor = Math.pow(10, validDecimal)
  return NP.divide(Math.floor(NP.times(value, factor)), factor)
}

// 预编译正则表达式提升性能
const THOUSAND_SEPARATOR_REGEX = /\B(?=(\d{3})+(?!\d))/g

/**
 * 格式化数字，添加千分位分隔符
 */
export function formatNumber(num: number | string, decimal: number = 2): string {
  const roundedNum = round(num, decimal)
  const fixedNum = roundedNum.toFixed(validateDecimal(decimal))
  const [integer, fraction] = fixedNum.split('.')
  const formattedInteger = integer.replace(THOUSAND_SEPARATOR_REGEX, ',')
  return fraction ? `${formattedInteger}.${fraction}` : formattedInteger
}

/**
 * 精确比较两个数字是否相等
 */
export function isEqual(a: number | string, b: number | string, precision: number = 10): boolean {
  if (!Number.isInteger(precision) || precision < 0 || precision > 15) {
    throw new Error(`精度必须是0-15之间的整数: ${precision}`)
  }
  return Math.abs(subtract(a, b)) < Math.pow(10, -precision)
}

/**
 * 精确比较 a 是否大于 b
 */
export function isGreater(a: number | string, b: number | string): boolean {
  return subtract(validateAndConvert(a), validateAndConvert(b)) > 0
}

/**
 * 精确比较 a 是否小于 b
 */
export function isLess(a: number | string, b: number | string): boolean {
  return subtract(validateAndConvert(a), validateAndConvert(b)) < 0
}

/**
 * 精确比较 a 是否大于等于 b
 */
export function isGreaterOrEqual(a: number | string, b: number | string): boolean {
  return subtract(validateAndConvert(a), validateAndConvert(b)) >= 0
}

/**
 * 精确比较 a 是否小于等于 b
 */
export function isLessOrEqual(a: number | string, b: number | string): boolean {
  return subtract(validateAndConvert(a), validateAndConvert(b)) <= 0
}

/**
 * 将数字转换为百分比字符串
 */
export function toPercent(num: number | string, decimal: number = 2): string {
  const percent = multiply(num, 100)
  return `${round(percent, decimal)}%`
}

/**
 * 转换为固定小数位的字符串（金钱格式）
 */
export function toFixed(num: number | string, decimal: number = 2): string {
  return round(num, decimal).toFixed(validateDecimal(decimal))
}

/**
 * 获取数字的绝对值
 */
export function abs(num: number | string): number {
  return Math.abs(validateAndConvert(num))
}

/**
 * 链式计算器（基于 number-precision）
 */
export class Calculator {
  private value: number

  constructor(initialValue: number | string = 0) {
    this.value = validateAndConvert(initialValue)
  }

  add(num: number | string): Calculator {
    this.value = NP.plus(this.value, validateAndConvert(num))
    return this
  }

  subtract(num: number | string): Calculator {
    this.value = NP.minus(this.value, validateAndConvert(num))
    return this
  }

  multiply(num: number | string): Calculator {
    this.value = NP.times(this.value, validateAndConvert(num))
    return this
  }

  divide(num: number | string): Calculator {
    const divisor = validateAndConvert(num)
    if (divisor === 0) {
      throw new Error('除数不能为0')
    }
    this.value = NP.divide(this.value, divisor)
    return this
  }

  round(decimal: number = 2): Calculator {
    this.value = NP.round(this.value, validateDecimal(decimal))
    return this
  }

  abs(): Calculator {
    this.value = Math.abs(this.value)
    return this
  }

  getValue(): number {
    return this.value
  }

  toString(decimal?: number): string {
    return decimal !== undefined ? round(this.value, decimal).toString() : this.value.toString()
  }

  toFixed(decimal: number = 2): string {
    return round(this.value, validateDecimal(decimal)).toFixed(validateDecimal(decimal))
  }
}

/**
 * 创建计算器实例
 */
export function calculator(initialValue: number | string = 0): Calculator {
  return new Calculator(initialValue)
}

/**
 * 金钱计算专用工具
 */
export const Money = {
  /**
   * 元转分（避免小数计算）
   */
  yuanToCent(yuan: number | string): number {
    return round(multiply(yuan, 100), 0)
  },

  /**
   * 分转元
   */
  centToYuan(cent: number | string): number {
    return divide(cent, 100)
  },

  /**
   * 格式化金钱显示（保留2位小数，添加千分位）
   */
  format(amount: number | string): string {
    return formatNumber(amount, 2)
  },

  /**
   * 金钱计算器
   */
  calc(amount: number | string): Calculator {
    return new Calculator(amount)
  },

  /**
   * 计算折扣后价格 (discountRate: 0.1 表示1折，0.9表示9折)
   */
  discount(price: number | string, discountRate: number | string): number {
    const rate = validateAndConvert(discountRate)
    if (rate < 0 || rate > 1) {
      throw new Error(`折扣率必须在0-1之间: ${rate}`)
    }
    return multiply(price, rate)
  },

  /**
   * 计算税后价格
   */
  withTax(price: number | string, taxRate: number | string): number {
    const rate = validateAndConvert(taxRate)
    if (rate < 0) {
      throw new Error(`税率不能为负数: ${rate}`)
    }
    return multiply(price, add(1, rate))
  },
}
