import { defineStore } from 'pinia'
import { USER_TYPE } from '@/enum'
import type { sysUserCommonPhraseQueryListInt } from '@/service/sysUserCommonPhrase/types'

type CommonPhrases = {
  [K in USER_TYPE]: sysUserCommonPhraseQueryListInt[]
}

export const usePhrasesCommonStore = defineStore(
  'phrases',
  () => {
    /** 招呼语缺醒默认 */
    const phrasesGreetDefault = ref<CommonPhrases>({
      [USER_TYPE.APPLICANT]: [
        {
          id: null,
          content: '您好，看到贵公司在招岗位，我的经验比较匹配，想了解下是否还有机会？',
          sortNo: 1,
          systemDefaultKey: 'app_greet_001',
          isDefault: 0,
        },
        {
          id: null,
          content: '打扰了，对您发布的岗位很感兴趣，能简单沟通下具体要求吗？',
          sortNo: 2,
          systemDefaultKey: 'app_greet_002',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，已投递贵公司岗位，想问问简历审核进度如何？',
          sortNo: 3,
          systemDefaultKey: 'app_greet_003',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，关注到贵公司的招聘信息，我的技能应该适合相关岗位，盼回复。',
          sortNo: 4,
          systemDefaultKey: 'app_greet_004',
          isDefault: 0,
        },
        {
          id: null,
          content: '打扰了，想了解下贵公司目前在招岗位的基本情况，我的履历或许匹配。',
          sortNo: 5,
          systemDefaultKey: 'app_greet_005',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，投递了贵公司的岗位，希望能有进一步沟通的机会，麻烦告知下情况。',
          sortNo: 6,
          systemDefaultKey: 'app_greet_006',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，看到贵公司有岗位招聘，我过往经验比较对口，能聊聊吗？',
          sortNo: 7,
          systemDefaultKey: 'app_greet_007',
          isDefault: 0,
        },
        {
          id: null,
          content: '打扰了，对贵公司的岗位很感兴趣，想知道是否还在招人？',
          sortNo: 8,
          systemDefaultKey: 'app_greet_008',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，已投简历，想了解下后续是否有面试安排，期待回复。',
          sortNo: 9,
          systemDefaultKey: 'app_greet_009',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，贵公司的岗位和我的职业方向契合，能简单介绍下吗？',
          sortNo: 10,
          systemDefaultKey: 'app_greet_010',
          isDefault: 0,
        },
        {
          id: null,
          content: '打扰了，看到招聘信息，我的能力应该能胜任相关岗位，想争取个机会。',
          sortNo: 11,
          systemDefaultKey: 'app_greet_011',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，投递了岗位简历，想问问是否通过初筛，盼告知。',
          sortNo: 12,
          systemDefaultKey: 'app_greet_012',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，贵公司在招的岗位我很感兴趣，能沟通下具体工作内容吗？',
          sortNo: 13,
          systemDefaultKey: 'app_greet_013',
          isDefault: 0,
        },
        {
          id: null,
          content: '打扰了，我的经验和贵公司岗位要求比较匹配，想了解下招聘进展。',
          sortNo: 14,
          systemDefaultKey: 'app_greet_014',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，关注贵公司很久了，看到有岗位在招，希望能有机会合作，能聊聊吗？',
          sortNo: 15,
          systemDefaultKey: 'app_greet_015',
          isDefault: 0,
        },
      ],
      [USER_TYPE.HR]: [
        {
          id: null,
          content:
            '您好，刷到您的简历啦，感觉您的专业背景和我们公司的 [岗位] 超契合，要不要抽空沟通下？',
          sortNo: 1,
          systemDefaultKey: 'hr_greet_001',
          isDefault: 0,
        },
        {
          id: null,
          content:
            '您好，我们公司正在招聘 [岗位]，看了您的简历后觉得很合适，想简单介绍下岗位情况可以吗？',
          sortNo: 2,
          systemDefaultKey: 'hr_greet_002',
          isDefault: 0,
        },
        {
          id: null,
          content: '哈喽，刚浏览到您的简历，发现您的求职方向和我们的 [岗位] 高度匹配，方便聊聊吗？',
          sortNo: 3,
          systemDefaultKey: 'hr_greet_003',
          isDefault: 0,
        },
        {
          id: null,
          content:
            '您好，您的求职意向和我们的 [岗位] 很匹配，想和您聊聊岗位要求、工作内容这些细节～',
          sortNo: 4,
          systemDefaultKey: 'hr_greet_004',
          isDefault: 0,
        },
        {
          id: null,
          content:
            '哈喽，注意到您关注我们公司挺久了，现在 [岗位] 有空缺，您的简历和岗位很搭，想聊聊您对岗位的期待吗？',
          sortNo: 5,
          systemDefaultKey: 'hr_greet_005',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，看了您的简历，感觉和我们正在招聘的岗位挺匹配的，想简单聊聊吗？',
          sortNo: 6,
          systemDefaultKey: 'hr_greet_006',
          isDefault: 0,
        },
        {
          id: null,
          content: '哈喽，您的求职意向和我们的岗位需求很契合，方便沟通下岗位细节吗？',
          sortNo: 7,
          systemDefaultKey: 'hr_greet_007',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好呀，浏览到您的简历，觉得您比较适合我们这个岗位，想了解下您的想法～',
          sortNo: 8,
          systemDefaultKey: 'hr_greet_008',
          isDefault: 0,
        },
        {
          id: null,
          content: '嗨，注意到您的简历内容，和我们招聘的岗位匹配度不错，能聊聊吗？',
          sortNo: 9,
          systemDefaultKey: 'hr_greet_009',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，我们正在招 [岗位名称]，您的经历看起来很合适，想简单介绍下岗位情况～',
          sortNo: 10,
          systemDefaultKey: 'hr_greet_010',
          isDefault: 0,
        },
        {
          id: null,
          content: '哈喽，看了您的简历，觉得您挺符合我们的招聘要求，有空沟通下吗？',
          sortNo: 11,
          systemDefaultKey: 'hr_greet_011',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好呀，您的技能背景和我们这个岗位挺搭的，想聊聊具体的工作内容吗？',
          sortNo: 12,
          systemDefaultKey: 'hr_greet_012',
          isDefault: 0,
        },
        {
          id: null,
          content: '嗨，发现您的简历和我们的岗位需求很匹配，能简单交流下吗？',
          sortNo: 13,
          systemDefaultKey: 'hr_greet_013',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，我们在招 [岗位名称]，看了您的简历觉得挺合适的，想聊聊机会吗？',
          sortNo: 14,
          systemDefaultKey: 'hr_greet_014',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好呀，浏览您的简历后，觉得您适合我们的岗位，方便沟通下吗？',
          sortNo: 15,
          systemDefaultKey: 'hr_greet_015',
          isDefault: 0,
        },
        {
          id: null,
          content: '嗨，注意到您对 [行业 / 岗位] 感兴趣，我们正好在招聘，想聊聊吗？',
          sortNo: 16,
          systemDefaultKey: 'hr_greet_016',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好，您的简历内容和我们的招聘需求挺配的，想简单介绍下岗位～',
          sortNo: 17,
          systemDefaultKey: 'hr_greet_017',
          isDefault: 0,
        },
        {
          id: null,
          content: '哈喽，看了您的简历，感觉您比较适合我们这个岗位，能聊聊您的想法吗？',
          sortNo: 18,
          systemDefaultKey: 'hr_greet_018',
          isDefault: 0,
        },
        {
          id: null,
          content: '您好呀，我们正在招聘 [岗位名称]，您的经历看起来很合适，想沟通下吗？',
          sortNo: 19,
          systemDefaultKey: 'hr_greet_019',
          isDefault: 0,
        },
      ],
    })
    return {
      phrasesGreetDefault,
    }
  },
  {
    persist: true,
  },
)
