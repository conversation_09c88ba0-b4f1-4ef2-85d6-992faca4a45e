import { JobType } from '@/enum'

/** 岗位状态枚举 */
export enum PositionStatus {
  /** 草稿 */
  DRAFT = 0,
  /** 在招 */
  RECRUITING = 1,
  /** 关闭（hr正常关闭/超时关闭） */
  CLOSED = 2,
  /** 平台关闭（不可开启） */
  PLATFORM_CLOSED = 3,
}

export interface hrPositionAddDataInt {
  /** 公司工作地址id */
  companyWorkAddressId: number
  /** 工作类型 */
  jobType: number
  /** 岗位待遇 */
  positionBenefitList: string[]
  /** 岗位关键词 */
  positionKeyList: string[]
  /** 岗位code */
  positionCode: string
  /** 岗位描述 */
  positionDesc: string
  /** 岗位名称 */
  positionName: string
  /** 任职要求-学历要求数据字典10 */
  workEducational: number
  /** 工作经验自用 */
  workExperience?: number
  /** 任职要求-工作经验年限结束0代表不限 */
  workExperienceEnd: number
  /** 任职要求-工作经验年限开始0代表不限 */
  workExperienceStart: number
  /** 薪资范围自用 */
  workSalary?: string[]
  /** 岗位标签名称 */
  positionMarkName: string
  /** 岗位标签编码 */
  positionMarkCode: number
  /** 任职要求-薪资范围起始(单位元) */
  workSalaryBegin: number
  /** 任职要求-薪资范围截止(单位元) */
  workSalaryEnd: number
  /** 薪资结构 */
  salaryFixed: number | null
  /** 浮动薪资 */
  salaryFloat: number | null
  /** 薪资类型 */
  salaryType: number
}
export interface hrPositionAddInt {}

export interface hrPositionPayAndPublishDataInt {
  /** 岗位id */
  positionId: number
  /** 发布时效月份 */
  publishMonth: number
}

export interface hrPositionQueryMyPublishListDataInt {
  status: PositionStatus
}

export interface hrPositionQueryMyPublishListInt
  extends Pick<
      hrPositionAddDataInt,
      | 'jobType'
      | 'positionCode'
      | 'positionDesc'
      | 'positionName'
      | 'workEducational'
      | 'workExperience'
      | 'workSalaryBegin'
      | 'workSalaryEnd'
    >,
    Api.Request.CommonRecord<{
      /** 开始时间 */
      beginTime?: string
      /** 城市code */
      cityCode?: string
      /** 城市名称 */
      cityName?: string
      /** 公司id */
      companyId?: number
      /** 区code */
      districtCode?: string
      /** 区名称 */
      districtName?: string
      /** 截止时间 */
      endTime?: string
      /** 行业code */
      industryCode?: string
      /** 行业名称 */
      industryName?: string
      /** 人工视检状态0未检查1通过2不通过 */
      manualInspectionStatus?: number
      /** 视检时间 */
      manualInspectionTime?: string
      /** 视检通过 */
      manualInspectionUserId?: number
      /** 视检人id */
      manualInspectionUserName?: string
      /** 岗位id */
      postingId?: number
      /** 省份code */
      provinceCode?: string
      /** 省份名称 */
      provinceName?: string
      /** 发布时间 */
      publishTime?: string
      /** 视检不通过原因 */
      reason?: string
      /** 薪资类型 12~25薪 */
      wordSalaryType?: number
      /** 工作地点(不用省市区) */
      workLocation?: string
    }> {}

export interface hrPositionQueryOptionListDataInt {}
export interface hrPositionQueryOptionListInt
  extends Partial<
      Pick<
        hrPositionAddDataInt,
        | 'positionCode'
        | 'positionName'
        | 'workEducational'
        | 'workSalaryBegin'
        | 'workSalaryEnd'
        | 'positionMarkName'
      >
    >,
    Partial<
      Pick<
        hrPositionQueryMyPublishListInt,
        'cityCode' | 'cityName' | 'districtCode' | 'districtName' | 'provinceCode' | 'provinceName'
      >
    > {
  /** 岗位主键id */
  id?: number
  /** 岗位关键词(逗号分割 取第一个) */
  positionKeyList?: string[]
  /** 工作年限要求结束年(0为不限) */
  workExperienceEnd?: number
  /** 工作年限要求开始年(0为不限) */
  workExperienceStart?: number
}
